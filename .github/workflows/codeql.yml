# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
#
# ******** NOTE ********
# We have attempted to detect the languages in your repository. Please check
# the `language` matrix defined below to confirm you have the correct set of
# supported CodeQL languages.
#
name: "CodeQL"

on:
  push:
    branches: [ "develop", release/* ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ "develop" ]

jobs:
  analyze-java:
    name: Analyze (Java)
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'corretto'
          cache: 'gradle'
      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: java
      - run: |
          make _build-java
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:java"

  #  analyze-js-core:
  #    name: Analyze (javascript-core)
  #    runs-on: ubuntu-latest
  #    permissions:
  #      actions: read
  #      contents: read
  #      security-events: write
  #    steps:
  #    - name: Checkout repository
  #      uses: actions/checkout@v4
  #    # Initializes the CodeQL tools for scanning.
  #    - name: Initialize CodeQL
  #      uses: github/codeql-action/init@v2
  #      with:
  #        languages: javascript
  #    - run: |
  #        make _build-core
  #    - name: Perform CodeQL Analysis
  #      uses: github/codeql-action/analyze@v2
  #      with:
  #        category: "/language:javascript"

  #  analyze-js-room:
  #    name: Analyze (javascript-room)
  #    runs-on: ubuntu-latest
  #    permissions:
  #      actions: read
  #      contents: read
  #      security-events: write
  #    steps:
  #    - name: Checkout repository
  #      uses: actions/checkout@v4
  #    - name: Install build-essential
  #      run: sudo apt-get update -y && sudo apt-get install -y build-essential
  #    - name: Set up Rust
  #      uses: actions-rs/toolchain@v1
  #      with:
  #        toolchain: nightly
  #        profile: minimal
  #        override: true
  #    # Initializes the CodeQL tools for scanning.
  #    - name: Initialize CodeQL
  #      uses: github/codeql-action/init@v2
  #      with:
  #        languages: javascript
  #    - run: |
  #        make _build-room
  #    - name: Perform CodeQL Analysis
  #      uses: github/codeql-action/analyze@v2
  #      with:
  #        category: "/language:javascript"

  # analyze-js-web:
  #   name: Analyze (javascript-web)
  #   runs-on: ubuntu-latest
  #   permissions:
  #     actions: read
  #     contents: read
  #     security-events: write
  #   steps:
  #   - name: Checkout repository
  #     uses: actions/checkout@v4
  #   # Initializes the CodeQL tools for scanning.
  #   - name: Initialize CodeQL
  #     uses: github/codeql-action/init@v2
  #     with:
  #       languages: javascript
  #   - run: |
  #       make _build-web
  #   - name: Perform CodeQL Analysis
  #     uses: github/codeql-action/analyze@v2
  #     with:
  #       category: "/language:javascript"

  # analyze:
  #   name: Analyze
  #   runs-on: ubuntu-latest
  #   permissions:
  #     actions: read
  #     contents: read
  #     security-events: write

  #   strategy:
  #     fail-fast: false
  #     matrix:
  #       language: [ 'java', 'javascript' ]
  #       # CodeQL supports [ 'cpp', 'csharp', 'go', 'java', 'javascript', 'python', 'ruby' ]
  #       # Use only 'java' to analyze code written in Java, Kotlin or both
  #       # Use only 'javascript' to analyze code written in JavaScript, TypeScript or both
  #       # Learn more about CodeQL language support at https://aka.ms/codeql-docs/language-support

  #   steps:
  #   - name: Checkout repository
  #     uses: actions/checkout@v4

  #   # Initializes the CodeQL tools for scanning.
  #   - name: Initialize CodeQL
  #     uses: github/codeql-action/init@v2
  #     with:
  #       languages: ${{ matrix.language }}
  #       # If you wish to specify custom queries, you can do so here or in a config file.
  #       # By default, queries listed here will override any specified in a config file.
  #       # Prefix the list here with "+" to use these queries and those in the config file.

  #       # Details on CodeQL's query packs refer to : https://docs.github.com/en/code-security/code-scanning/automatically-scanning-your-code-for-vulnerabilities-and-errors/configuring-code-scanning#using-queries-in-ql-packs
  #       # queries: security-extended,security-and-quality


  #   # Autobuild attempts to build any compiled languages  (C/C++, C#, Go, or Java).
  #   # If this step fails, then you should remove it and run the build manually (see below)
  #   # - name: Autobuild
  #   #   uses: github/codeql-action/autobuild@v2

  #   # ℹ️ Command-line programs to run using the OS shell.
  #   # 📚 See https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#jobsjob_idstepsrun

  #   #   If the Autobuild fails above, remove it and uncomment the following three lines.
  #   #   modify them (or add more) to build your code if your project, please refer to the EXAMPLE below for guidance.

  #   - run: |
  #       echo "Run, Build Application"
  #       make build

  #   - name: Perform CodeQL Analysis
  #     uses: github/codeql-action/analyze@v2
  #     with:
  #       category: "/language:${{matrix.language}}"
