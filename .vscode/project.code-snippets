{
	// Place your datasheet-fix workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and 
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope 
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is 
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are: 
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. 
	// Placeholders with the same ids are connected.

	// Create React function component template code based on filename
	"Custom React functional component": {
		"prefix": "rfc",
		"scope": "typescriptreact",
		"body": [
			"import React, { FC } from 'react';",
			"",
			"interface I$1Props {",
			"\t$2",
			"}",
			"",
			"export const ${1:${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}}: FC<I$1Props> = (props) => {",
			"\treturn (",
			"\t\t<div>",
			"\t\t\t$0",
			"\t\t</div>",
			"\t);",
			"};",
			"",
		],
		"description": "React Functional Component"
	},
	// t(Strings.<your_key>) for internationalization
	"t(Strings.<your_key>)": {
		"prefix": "tstrings",
		"scope": "typescript,typescriptreact",
		"body": [
			"t(Strings.$1)$0",
		],
		"description": "t(Strings.<your_key>)"
	}
}